#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块
负责配置文件的读取、写入和管理
"""

import json
import os
from typing import Dict, Optional


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_dir: str = "配置文件"):
        """
        初始化配置管理器
        
        Args:
            config_dir: 配置文件目录
        """
        self.config_dir = config_dir
        self.config_file = os.path.join(config_dir, "用户配置.json")
        self._ensure_config_dir()
        
    def _ensure_config_dir(self):
        """确保配置目录存在"""
        if not os.path.exists(self.config_dir):
            os.makedirs(self.config_dir)
    
    def load_config(self) -> Dict:
        """
        加载配置文件

        Returns:
            配置字典
        """
        if not os.path.exists(self.config_file):
            return self._get_default_config()

        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)

                # 迁移旧的键名到新的中文（英文）格式
                config = self._migrate_config_keys(config)

                # 确保配置包含所有必要字段
                default_config = self._get_default_config()
                for key, value in default_config.items():
                    if key not in config:
                        config[key] = value

                # 保存迁移后的配置
                self.save_config(config)
                return config
        except (json.JSONDecodeError, FileNotFoundError):
            return self._get_default_config()
    
    def save_config(self, config: Dict) -> bool:
        """
        保存配置文件
        
        Args:
            config: 配置字典
            
        Returns:
            是否保存成功
        """
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=4)
            return True
        except Exception as e:
            print(f"保存配置失败: {e}")
            return False
    
    def _get_default_config(self) -> Dict:
        """
        获取默认配置

        Returns:
            默认配置字典
        """
        return {
            "认证令牌(authorization)": "",
            "会话信息(cookie)": "",
            "用户信息(user_info)": {},
            "最后验证时间(last_validation_time)": "",
            "验证状态(validation_status)": False,
            "自动验证(auto_validate)": True,
            "主题(theme)": "light"
        }
    
    def update_auth_info(self, authorization: str, cookie: str) -> bool:
        """
        更新认证信息

        Args:
            authorization: Authorization头
            cookie: Cookie信息

        Returns:
            是否更新成功
        """
        config = self.load_config()
        config["认证令牌(authorization)"] = authorization
        config["会话信息(cookie)"] = cookie
        config["验证状态(validation_status)"] = False  # 重置验证状态
        config["用户信息(user_info)"] = {}  # 清空用户信息
        return self.save_config(config)
    
    def update_validation_result(self, is_valid: bool, user_info: Dict = None) -> bool:
        """
        更新验证结果

        Args:
            is_valid: 是否验证成功
            user_info: 用户信息

        Returns:
            是否更新成功
        """
        config = self.load_config()
        config["验证状态(validation_status)"] = is_valid
        config["用户信息(user_info)"] = user_info or {}

        # 更新验证时间
        from datetime import datetime
        config["最后验证时间(last_validation_time)"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        return self.save_config(config)
    
    def get_auth_headers(self) -> Dict[str, str]:
        """
        获取认证头信息
        
        Returns:
            认证头字典
        """
        config = self.load_config()
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            "Connection": "keep-alive",
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-origin"
        }
        
        if config.get("认证令牌(authorization)"):
            headers["Authorization"] = config["认证令牌(authorization)"]

        if config.get("会话信息(cookie)"):
            headers["Cookie"] = config["会话信息(cookie)"]
            
        return headers
    
    def is_auth_configured(self) -> bool:
        """
        检查是否已配置认证信息
        
        Returns:
            是否已配置
        """
        config = self.load_config()
        return bool(config.get("认证令牌(authorization)") and config.get("会话信息(cookie)"))
    
    def get_user_info(self) -> Dict:
        """
        获取用户信息
        
        Returns:
            用户信息字典
        """
        config = self.load_config()
        return config.get("用户信息(user_info)", {})

    def _migrate_config_keys(self, config: Dict) -> Dict:
        """
        迁移旧的配置键名到新的中文（英文）格式

        Args:
            config: 原始配置字典

        Returns:
            迁移后的配置字典
        """
        # 键名映射表
        key_mapping = {
            "authorization": "认证令牌(authorization)",
            "cookie": "会话信息(cookie)",
            "user_info": "用户信息(user_info)",
            "last_validation_time": "最后验证时间(last_validation_time)",
            "validation_status": "验证状态(validation_status)",
            "auto_validate": "自动验证(auto_validate)",
            "theme": "主题(theme)"
        }

        migrated_config = {}

        # 迁移已存在的键
        for old_key, new_key in key_mapping.items():
            if old_key in config:
                # 如果新键不存在，则迁移旧键的值
                if new_key not in config:
                    migrated_config[new_key] = config[old_key]
                else:
                    # 如果新键已存在，保留新键的值
                    migrated_config[new_key] = config[new_key]
            elif new_key in config:
                # 如果只有新键存在，保留新键
                migrated_config[new_key] = config[new_key]

        # 保留其他不在映射表中的键
        for key, value in config.items():
            if key not in key_mapping and key not in key_mapping.values():
                migrated_config[key] = value

        return migrated_config
