#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
阿奇索闲鱼自动发货工具
主程序入口
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

# 添加功能模块路径
sys.path.append(os.path.join(os.path.dirname(__file__), '功能模块'))

from 主界面 import MainWindow


def main():
    """主程序入口"""
    # 设置高DPI支持（必须在创建QApplication之前）
    QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

    app = QApplication(sys.argv)

    # 设置应用程序属性
    app.setApplicationName("商品数据解析工具")
    app.setApplicationVersion("2.0.0")
    app.setOrganizationName("数据解析工具")

    # 设置全局字体为微软雅黑，增大字体大小
    font = QFont("Microsoft YaHei", 11)
    font.setStyleHint(QFont.SansSerif)
    app.setFont(font)

    # 创建主窗口
    window = MainWindow()
    window.show()

    # 运行应用程序
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()