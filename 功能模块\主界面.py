#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主界面模块
商品数据解析工具的主界面
"""

import sys
from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QTextEdit, QPushButton, QFrame, QMessageBox,
    QProgressBar, QGroupBox, QGridLayout,
    QFileDialog
)
from PyQt5.QtCore import Qt, pyqtSlot
from PyQt5.QtGui import QFont, QPixmap

from 商品数据解析 import ProductParserManager


class MainWindow(QMainWindow):
    """主窗口类"""

    def __init__(self):
        super().__init__()
        self.parser_manager = ProductParserManager(self)
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("商品数据解析工具 v1.6")
        self.setGeometry(100, 100, 1000, 700)
        self.setMinimumSize(800, 600)

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局（水平布局）
        main_layout = QHBoxLayout(central_widget)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(15, 15, 15, 15)

        # 创建左侧功能区域
        self.create_left_panel(main_layout)

        # 创建右侧日志区域
        self.create_right_panel(main_layout)

        # 应用样式
        self.apply_styles()
        
    def create_left_panel(self, parent_layout):
        """创建左侧功能面板"""
        left_widget = QWidget()
        left_widget.setObjectName("leftPanel")
        left_widget.setFixedWidth(350)
        left_layout = QVBoxLayout(left_widget)
        left_layout.setSpacing(15)
        left_layout.setContentsMargins(0, 0, 0, 0)

        # 创建商品数据解析区域
        self.create_parser_section(left_layout)

        # 创建日志区域
        self.create_log_section(left_layout)

        parent_layout.addWidget(left_widget)
        


    def create_parser_section(self, parent_layout):
        """创建商品数据解析区域"""
        parser_group = QGroupBox("商品数据解析")
        parser_group.setObjectName("parserGroup")
        parser_layout = QVBoxLayout(parser_group)

        # 解析按钮区域
        parse_layout = QGridLayout()

        # 单个解析按钮
        self.single_parse_button = QPushButton("单个解析")
        self.single_parse_button.setObjectName("singleParseButton")
        self.single_parse_button.clicked.connect(self.single_parse_file)

        # 批量解析按钮
        self.batch_parse_button = QPushButton("批量解析")
        self.batch_parse_button.setObjectName("batchParseButton")
        self.batch_parse_button.clicked.connect(self.batch_parse_folder)

        parse_layout.addWidget(self.single_parse_button, 0, 0)
        parse_layout.addWidget(self.batch_parse_button, 0, 1)

        parser_layout.addLayout(parse_layout)

        parent_layout.addWidget(parser_group)

    def create_log_section(self, parent_layout):
        """创建日志区域"""
        log_group = QGroupBox("操作日志")
        log_group.setObjectName("logGroup")
        log_layout = QVBoxLayout(log_group)

        # 日志显示区域 - 增加高度，充分利用空间
        self.log_display = QTextEdit()
        self.log_display.setObjectName("logDisplay")
        self.log_display.setReadOnly(True)
        self.log_display.setPlaceholderText("操作日志将在此显示...")
        # 移除最大高度限制，让日志框充分利用空间
        self.log_display.setMinimumHeight(300)

        # 状态区域 - 放在日志框下方
        status_frame = QFrame()
        status_frame.setObjectName("statusFrame")
        status_layout = QVBoxLayout(status_frame)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setObjectName("progressBar")
        self.progress_bar.setVisible(False)

        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_label.setObjectName("statusLabel")
        self.status_label.setAlignment(Qt.AlignCenter)

        status_layout.addWidget(self.progress_bar)
        status_layout.addWidget(self.status_label)

        # 日志控制按钮 - 放在最底部
        log_control_layout = QHBoxLayout()

        self.clear_log_button = QPushButton("清空日志")
        self.clear_log_button.setObjectName("clearLogButton")
        self.clear_log_button.clicked.connect(self.clear_log)

        self.save_log_button = QPushButton("保存日志")
        self.save_log_button.setObjectName("saveLogButton")
        self.save_log_button.clicked.connect(self.save_log_placeholder)

        log_control_layout.addWidget(self.clear_log_button)
        log_control_layout.addWidget(self.save_log_button)
        log_control_layout.addStretch()

        # 按新的顺序添加组件：日志框 -> 状态区域 -> 按钮
        log_layout.addWidget(self.log_display)
        log_layout.addWidget(status_frame)
        log_layout.addLayout(log_control_layout)

        parent_layout.addWidget(log_group)

    def create_right_panel(self, parent_layout):
        """创建右侧审查面板"""
        right_widget = QWidget()
        right_widget.setObjectName("rightPanel")
        right_layout = QVBoxLayout(right_widget)
        right_layout.setSpacing(10)
        right_layout.setContentsMargins(0, 0, 0, 0)

        # 审查区域
        review_group = QGroupBox("内容审查")
        review_group.setObjectName("reviewGroup")
        review_layout = QVBoxLayout(review_group)

        # 文本内容显示区域
        text_label = QLabel("商品文本内容:")
        text_label.setObjectName("textLabel")

        self.text_display = QTextEdit()
        self.text_display.setObjectName("textDisplay")
        self.text_display.setReadOnly(True)
        self.text_display.setPlaceholderText("解析的商品文本内容将在此显示...")
        # 设置固定高度，为图片区域留出更多空间
        self.text_display.setFixedHeight(180)

        # 图片显示区域
        image_label = QLabel("商品图片:")
        image_label.setObjectName("imageLabel")

        self.image_display = QLabel()
        self.image_display.setObjectName("imageDisplay")
        self.image_display.setAlignment(Qt.AlignCenter)
        self.image_display.setStyleSheet("border: 1px solid #ced4da; background-color: #f8f9fa;")
        # 让图片区域充分利用剩余空间
        self.image_display.setMinimumHeight(400)
        self.image_display.setText("解析的商品图片将在此显示...")

        review_layout.addWidget(text_label)
        review_layout.addWidget(self.text_display)
        review_layout.addWidget(image_label)
        review_layout.addWidget(self.image_display, 1)  # 添加拉伸因子，让图片区域占用更多空间

        right_layout.addWidget(review_group)

        parent_layout.addWidget(right_widget)
        
    def apply_styles(self):
        """应用样式表"""
        style = """
        * {
            font-family: "Microsoft YaHei", "微软雅黑", sans-serif;
        }

        QMainWindow {
            background-color: #f8f9fa;
        }

        #leftPanel {
            background-color: white;
            border-radius: 8px;
            padding: 15px;
            margin-right: 5px;
        }

        #rightPanel {
            background-color: white;
            border-radius: 8px;
            padding: 15px;
            margin-left: 5px;
        }

        #statusFrame {
            background-color: #f8f9fa;
            border-radius: 5px;
            padding: 10px;
            margin-top: 10px;
        }
        
        QGroupBox {
            font-weight: bold;
            font-size: 14px;
            color: #2c3e50;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            margin-top: 8px;
            padding-top: 8px;
            background-color: #fafbfc;
        }

        QGroupBox::title {
            subcontrol-origin: margin;
            left: 8px;
            padding: 0 6px 0 6px;
            background-color: #fafbfc;
        }

        #userNickname {
            font-size: 13px;
            padding: 3px 6px;
            border-radius: 3px;
            background-color: #f8f9fa;
        }

        QLabel {
            font-size: 12px;
        }
        
        QPushButton {
            background-color: white;
            color: black;
            border: 1px solid #ced4da;
            padding: 10px 18px;
            border-radius: 5px;
            font-weight: normal;
            font-size: 13px;
            min-height: 22px;
            min-width: 100px;
            max-height: 22px;
            max-width: 100px;
        }

        QPushButton:hover {
            background-color: #f8f9fa;
            border-color: #adb5bd;
        }

        QPushButton:pressed {
            background-color: #e9ecef;
            border-color: #6c757d;
        }

        QPushButton:disabled {
            background-color: #f8f9fa;
            color: #6c757d;
            border-color: #dee2e6;
        }
        
        QTextEdit {
            border: 1px solid #ced4da;
            border-radius: 5px;
            padding: 8px;
            background-color: #ffffff;
            font-family: "Microsoft YaHei", "微软雅黑", sans-serif;
            font-size: 12px;
        }

        #logDisplay {
            background-color: #f8f9fa;
            color: #495057;
            line-height: 1.5;
            font-size: 12px;
        }

        #statusLabel {
            font-size: 13px;
            color: #6c757d;
            padding: 6px;
            background-color: transparent;
        }

        QProgressBar {
            border: 1px solid #ced4da;
            border-radius: 5px;
            text-align: center;
            background-color: #e9ecef;
            height: 20px;
        }

        QProgressBar::chunk {
            background-color: #007bff;
            border-radius: 4px;
        }

        #imageDisplay {
            border: 2px solid #dee2e6;
            border-radius: 8px;
            background-color: #ffffff;
            padding: 10px;
        }

        #textDisplay {
            border: 1px solid #ced4da;
            border-radius: 5px;
            background-color: #ffffff;
            padding: 8px;
            font-size: 12px;
            line-height: 1.4;
        }
        """
        self.setStyleSheet(style)

        # 初始化日志
        self.add_log("程序启动完成")

    def add_log(self, message: str):
        """添加日志"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        self.log_display.append(log_message)

        # 自动滚动到底部
        scrollbar = self.log_display.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    def clear_log(self):
        """清空日志"""
        self.log_display.clear()
        self.add_log("日志已清空")

    def save_log_placeholder(self):
        """保存日志占位功能"""
        self.add_log("保存日志功能待开发")
        self.show_message("提示", "保存日志功能待开发", QMessageBox.Information)

    def single_parse_file(self):
        """单个解析文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择商品数据JSON文件",
            "",
            "JSON文件 (*.json)"
        )

        if file_path:
            self.add_log(f"选择了文件: {file_path}")
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            self.parser_manager.parse_single_file(file_path)

    def batch_parse_folder(self):
        """批量解析文件夹"""
        folder_path = QFileDialog.getExistingDirectory(
            self,
            "选择包含JSON文件的文件夹"
        )

        if folder_path:
            self.add_log(f"选择了文件夹: {folder_path}")
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            self.parser_manager.parse_batch_folder(folder_path)



    def update_progress(self, value):
        """更新进度条"""
        self.progress_bar.setValue(value)

    def update_status(self, status):
        """更新状态标签"""
        self.status_label.setText(status)

    @pyqtSlot(bool, str)
    def on_parsing_finished(self, success, message):
        """解析完成回调"""
        self.progress_bar.setVisible(False)

        if success:
            self.status_label.setText("✓ 解析完成")
            self.show_message("成功", f"解析完成: {message}", QMessageBox.Information)
            # 显示解析的内容
            self.display_parsed_content()
        else:
            self.status_label.setText(f"✗ 解析失败: {message}")
            self.show_message("解析失败", f"解析失败: {message}", QMessageBox.Warning)

    def display_parsed_content(self):
        """显示解析的内容"""
        import os
        from pathlib import Path

        # 查找最新的商品文件夹
        product_folder_base = Path("商品文件夹")
        if not product_folder_base.exists():
            return

        # 获取最新修改的文件夹
        folders = [f for f in product_folder_base.iterdir() if f.is_dir()]
        if not folders:
            return

        latest_folder = max(folders, key=lambda f: f.stat().st_mtime)

        # 显示文本内容
        txt_file = latest_folder / "商品信息.txt"
        if txt_file.exists():
            try:
                with open(txt_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                self.text_display.setPlainText(content)
                self.add_log(f"已显示商品文本内容: {txt_file}")
            except Exception as e:
                self.add_log(f"读取文本文件失败: {str(e)}")

        # 显示第一张图片
        image_files = list(latest_folder.glob("图片_*.jpg")) + \
                     list(latest_folder.glob("图片_*.png")) + \
                     list(latest_folder.glob("图片_*.webp"))

        if image_files:
            # 按文件名排序，显示第一张
            image_files.sort()
            first_image = image_files[0]

            try:
                pixmap = QPixmap(str(first_image))
                if not pixmap.isNull():
                    # 获取显示区域的实际大小，留出一些边距
                    display_width = self.image_display.width() - 20
                    display_height = self.image_display.height() - 20

                    # 如果显示区域还没有正确初始化，使用默认大小
                    if display_width <= 0 or display_height <= 0:
                        display_width = 600
                        display_height = 400

                    # 缩放图片以适应显示区域
                    scaled_pixmap = pixmap.scaled(
                        display_width,
                        display_height,
                        Qt.KeepAspectRatio,
                        Qt.SmoothTransformation
                    )
                    self.image_display.setPixmap(scaled_pixmap)
                    self.add_log(f"已显示商品图片: {first_image.name}")
                else:
                    self.image_display.setText("图片加载失败")
                    self.add_log(f"图片加载失败: {first_image}")
            except Exception as e:
                self.image_display.setText("图片显示出错")
                self.add_log(f"显示图片出错: {str(e)}")
        else:
            self.image_display.setText("未找到商品图片")
            self.add_log("未找到商品图片文件")



    def show_message(self, title: str, message: str, icon=QMessageBox.Information):
        """显示消息框"""
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setIcon(icon)
        msg_box.exec_()

    def closeEvent(self, event):
        """窗口关闭事件"""
        event.accept()
