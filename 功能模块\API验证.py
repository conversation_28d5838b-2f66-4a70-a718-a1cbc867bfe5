#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API验证模块
负责验证用户认证信息的有效性
"""

import requests
import json
from typing import Dict, Tuple, Optional
from PyQt5.QtCore import QThread, pyqtSignal


class ApiValidator(QThread):
    """API验证器（异步）"""
    
    # 信号定义
    validation_finished = pyqtSignal(bool, dict)  # 验证完成信号(是否成功, 用户信息)
    validation_error = pyqtSignal(str)  # 验证错误信号
    
    def __init__(self, authorization: str, cookie: str):
        """
        初始化API验证器
        
        Args:
            authorization: Authorization头
            cookie: Cookie信息
        """
        super().__init__()
        self.authorization = authorization
        self.cookie = cookie
        self.api_url = "https://aldsidle.agiso.com/api/User/GetUserInfo"
        
    def run(self):
        """运行验证"""
        try:
            is_valid, user_info = self._validate_auth()
            self.validation_finished.emit(is_valid, user_info)
        except Exception as e:
            self.validation_error.emit(str(e))
    
    def _validate_auth(self) -> Tuple[bool, Dict]:
        """
        验证认证信息
        
        Returns:
            (是否有效, 用户信息)
        """
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            "Connection": "keep-alive",
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-origin",
            "Referer": "https://aldsidle.agiso.com/",
            "Authorization": self.authorization,
            "Cookie": self.cookie
        }
        
        try:
            response = requests.get(
                self.api_url,
                headers=headers,
                timeout=10,
                verify=True
            )
            
            # 检查HTTP状态码
            if response.status_code != 200:
                return False, {"error": f"HTTP状态码: {response.status_code}"}
            
            # 解析响应JSON
            try:
                data = response.json()
            except json.JSONDecodeError:
                return False, {"error": "响应不是有效的JSON格式"}
            
            # 检查响应结构
            if not isinstance(data, dict):
                return False, {"error": "响应格式错误"}
            
            # 检查业务状态码
            if data.get("statusCode") != 200:
                return False, {"error": f"业务状态码: {data.get('statusCode')}"}
            
            # 检查是否成功
            if not data.get("succeeded", False):
                return False, {"error": "请求未成功"}
            
            # 提取用户信息
            user_data = data.get("data", {}).get("data", {})
            if not user_data:
                return False, {"error": "用户数据为空"}
            
            # 构建用户信息
            user_info = {
                "nickname": user_data.get("nickName", ""),
                "shop_name": user_data.get("shopName", ""),
                "platform_shop_id": user_data.get("platformShopId", ""),
                "is_pro": user_data.get("pro", False),
                "deadline": user_data.get("deadLine", ""),
                "token_expire_time": user_data.get("tokenExpireTime", ""),
                "token_expired": user_data.get("tokenExpired", True),
                "customer_id": user_data.get("customerId", ""),
                "create_time": user_data.get("createTime", ""),
                "balance_enough": user_data.get("idleService", {}).get("balanceEnough", False),
                "idle_up": user_data.get("idleService", {}).get("idlE_UP", False),
                "others_count": len(user_data.get("others", [])),
                "auth_platforms": user_data.get("authPlatforms", [])
            }
            
            # 检查token是否过期
            if user_info["token_expired"]:
                return False, {"error": "Token已过期", "user_info": user_info}
            
            return True, user_info
            
        except requests.exceptions.Timeout:
            return False, {"error": "请求超时"}
        except requests.exceptions.ConnectionError:
            return False, {"error": "网络连接错误"}
        except requests.exceptions.RequestException as e:
            return False, {"error": f"请求异常: {str(e)}"}
        except Exception as e:
            return False, {"error": f"未知错误: {str(e)}"}


class SyncApiValidator:
    """同步API验证器"""
    
    @staticmethod
    def validate_auth(authorization: str, cookie: str) -> Tuple[bool, Dict]:
        """
        同步验证认证信息
        
        Args:
            authorization: Authorization头
            cookie: Cookie信息
            
        Returns:
            (是否有效, 用户信息或错误信息)
        """
        validator = ApiValidator(authorization, cookie)
        return validator._validate_auth()
    
    @staticmethod
    def quick_validate(authorization: str, cookie: str) -> bool:
        """
        快速验证（只返回是否有效）
        
        Args:
            authorization: Authorization头
            cookie: Cookie信息
            
        Returns:
            是否有效
        """
        is_valid, _ = SyncApiValidator.validate_auth(authorization, cookie)
        return is_valid
